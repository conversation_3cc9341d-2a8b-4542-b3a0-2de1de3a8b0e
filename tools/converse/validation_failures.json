[{"column": "Zip", "value": "602", "pattern": "/^\\d{4,8}$/", "STHHLD": "00602:GOMEZ::304063HC"}, {"column": "Zip", "value": "911", "pattern": "/^\\d{4,8}$/", "STHHLD": "00911:KING::13CALLEE"}, {"column": "Zip", "value": "911", "pattern": "/^\\d{4,8}$/", "STHHLD": "00911:KING::13CALLEE"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLES SVGS BK/HOLYOKE", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:DUNN::989EASTS"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLES SVGS BK/HOLYOKE", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:DUNN::989EASTS"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLES SVGS BK/HOLYOKE", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:DUNN::989EASTS"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLES SVGS BK/HOLYOKE", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:DUNN::989EASTS"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLES SVGS BK/HOLYOKE", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:DUNN::989EASTS"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLES SVGS BK/HOLYOKE", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:DUNN::989EASTS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MISCELLANEOUS NALT BK *OTHER INSTITUTIONAL LENDERS", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MISCELLANEOUS NALT BK *OTHER INSTITUTIONAL LENDERS", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MISCELLANEOUS NALT BK *OTHER INSTITUTIONAL LENDERS", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MISCELLANEOUS NALT BK *OTHER INSTITUTIONAL LENDERS", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLES SVGS BK/HOLYOKE", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:DUNN::989EASTS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MISCELLANEOUS NALT BK *OTHER INSTITUTIONAL LENDERS", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01002:ROSENBERRY::PLEASANT"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01008:DECOTEAU::12HALLRD"}, {"column": "MortgageMostRecentLenderName", "value": "TOP FLITE FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01008:DECOTEAU::12HALLRD"}, {"column": "HomeSewer", "value": "4", "pattern": "/^[Y]$/", "STHHLD": "01008:DECOTEAU::12HALLRD"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01008:DECOTEAU::12HALLRD"}, {"column": "MortgageMostRecentLenderName", "value": "TOP FLITE FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01008:DECOTEAU::12HALLRD"}, {"column": "HomeSewer", "value": "4", "pattern": "/^[Y]$/", "STHHLD": "01008:DECOTEAU::12HALLRD"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FSB/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:ANIPKO::120HAMPD"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FSB/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:ANIPKO::120HAMPD"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FSB/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:ANIPKO::120HAMPD"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:CARROLL::86RAYLOS"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FSB/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:ANIPKO::120HAMPD"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FSB/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:ANIPKO::120HAMPD"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FSB/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:ANIPKO::120HAMPD"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FSB/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:ANIPKO::120HAMPD"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01013:CARROLL::86RAYLOS"}, {"column": "MortgageMostRecentLenderName", "value": "BAYBANK VLY TR (TR)", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01020:KERNICKI::147MEMOR"}, {"column": "MortgageMostRecentLenderName", "value": "CITIZENS BK/RI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01020:LEPAGE::STEBBINS"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01020:JABER::106CREST"}, {"column": "MortgageMostRecentLenderName", "value": "CONSTITUTIONAL FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01028:MORSE::118LEEST"}, {"column": "MortgageMostRecentLenderName", "value": "CONSTITUTIONAL FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01028:MORSE::118LEEST"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01028:TURNBERG::48EDMUND"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLE'S UNITED BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01028:TURNBERG::48EDMUND"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01036:GAYLOR::85KNOLLO"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01040:TARDY::389ELMSS"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01040:TARDY::389ELMSS"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01040:TARDY::389ELMSS"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01040:TARDY::389ELMSS"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01040:HAIRSTON::81JACKSO"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01040:AYALA::559621CA"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01050:LAFOUNTAIN::KNIGHTVI"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01050:LAFOUNTAIN::KNIGHTVI"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01050:LAFOUNTAIN::KNIGHTVI"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01050:LAFOUNTAIN::KNIGHTVI"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01057:AMADEIJR::37REEDOL"}, {"column": "MortgageMostRecentLenderName", "value": "DIME R/E SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01077:BISHOP::123COLLE"}, {"column": "MortgageMostRecentLenderName", "value": "DIME R/E SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01077:BISHOP::123COLLE"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:KING::132CITYV"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:KING::132CITYV"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:KING::132CITYV"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:LEE::555RUSSE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:LEE::555RUSSE"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01085:DAIGLE::88UNIONA"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01085:DAIGLE::88UNIONA"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:PCHELKA::WOODBRID"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:PCHELKA::WOODBRID"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01085:PCHELKA::WOODBRID"}, {"column": "CreditRating", "value": "H", "pattern": "/^[A-F]$/", "STHHLD": "01089:POIRIER::106NORMA"}, {"column": "CreditRating", "value": "H", "pattern": "/^[A-F]$/", "STHHLD": "01089:POIRIER::106NORMA"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:SLATER::79NORMAN"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01104:COMSTOCK::29EASTST"}, {"column": "HomeSalesTransactionCode", "value": "S", "pattern": "/^[R,N]$/", "STHHLD": "01104:REILLY::99ARDMOR"}, {"column": "HomeSalesTransactionCode", "value": "S", "pattern": "/^[R,N]$/", "STHHLD": "01105:WAMSHER::101606MU"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01105:QAINTERO::SARATOGA"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "MortgageMostRecentLenderName", "value": "POLISH NAT'L CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01106:FRIEDRICH::WESTMORE"}, {"column": "Mortgage2ndPurchaseAmount", "value": "2500", "pattern": "/^\\d{5,6}$/", "STHHLD": "01108:OPPENHEIMER::43DRAPER"}, {"column": "Mortgage2ndPurchaseLoanTypeCode", "value": "P", "pattern": "/^[C]$/", "STHHLD": "01108:OPPENHEIMER::43DRAPER"}, {"column": "Mortgage2ndPurchaseAmount", "value": "1940", "pattern": "/^\\d{5,6}$/", "STHHLD": "01109:LANCASTER::65EMBURY"}, {"column": "Mortgage2ndPurchaseAmount", "value": "1940", "pattern": "/^\\d{5,6}$/", "STHHLD": "01109:LANCASTER::65EMBURY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:HARRIS::68QUINCY"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01109:PARIS::305REEDS"}, {"column": "Mortgage2ndPurchaseLoanTypeCode", "value": "P", "pattern": "/^[C]$/", "STHHLD": "01109:PARIS::305REEDS"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "2301 BOSTON ROAD LLC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01109:PARIS::305REEDS"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:SANTIAGO::837316ST"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01109:SANTIAGO::837316ST"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01118:CEPHAS::30ABBOTT"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01118:CEPHAS::30ABBOTT"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01118:CEPHAS::30ABBOTT"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01118:CEPHAS::30ABBOTT"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01118:CEPHAS::30ABBOTT"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01118:CEPHAS::30ABBOTT"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLESBANK (HOLYOKE MA)", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01119:SUNDBERG::150ALMIR"}, {"column": "MortgageMostRecentLenderName", "value": "PEOPLESBANK (HOLYOKE MA)", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01119:SUNDBERG::150ALMIR"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01151:PEREZ::126BROTH"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01151:PEREZ::126BROTH"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FEDERAL HM LN BK/BOSTON", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01151:APPIAH::38MARTHA"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "MortgageMostRecentLenderName", "value": "OMEGA FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01201:SIBNER::98OLIVER"}, {"column": "Mortgage2ndPurchaseAmount", "value": "4410", "pattern": "/^\\d{5,6}$/", "STHHLD": "01226:KIRCHNERJR::EDGEMERE"}, {"column": "MortgageMostRecentLenderName", "value": "MULTIBANK/WESTERN MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01230:KIRIAKEDES::154101MA"}, {"column": "MortgageMostRecentLenderName", "value": "MULTIBANK/WESTERN MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01230:KIRIAKEDES::154101MA"}, {"column": "MortgageMostRecentLenderName", "value": "MULTIBANK/WESTERN MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01230:KIRIAKEDES::154101MA"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST MAGNUS FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01238:TURTZ::88020DEA"}, {"column": "HomeWater", "value": "4", "pattern": "/^[Y]$/", "STHHLD": "01267:JOHNSON::2221HANC"}, {"column": "HomeWater", "value": "4", "pattern": "/^[Y]$/", "STHHLD": "01267:JOHNSON::2221HANC"}, {"column": "HomePurchasePrice", "value": "5", "pattern": "/^\\d{2,4}$/", "STHHLD": "01301:DAVIS::36BANKRO"}, {"column": "HomePurchasePrice", "value": "5", "pattern": "/^\\d{2,4}$/", "STHHLD": "01301:DAVIS::36BANKRO"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01420:CRUZ::42CLIFFS"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01436:COLEMAN::39ELMST"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01436:COLEMAN::39ELMST"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01436:COLEMAN::39ELMST"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01436:COLEMAN::39ELMST"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01436:BRESCIA::BALDWINV"}, {"column": "MortgageMostRecentLenderName", "value": "S&P TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01440:WALTON::74PARKER"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "MortgageMostRecentLenderName", "value": "LOANSNAP.COM INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01450:TALBOT::111GRATU"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "SIDUS FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01453:CORMIER::8TH21APT"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "SIDUS FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01453:CORMIER::8TH21APT"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "SIDUS FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01453:CORMIER::8TH21APT"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01453:HOYTE::MANCHEST"}, {"column": "MortgageMostRecentLenderName", "value": "PICKARD TR (RT)", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01460:MILLER::65BRUCES"}, {"column": "MortgageMostRecentLenderName", "value": "PICKARD TR (RT)", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01460:MILLER::65BRUCES"}, {"column": "MortgageMostRecentLenderName", "value": "PICKARD TR (RT)", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01460:MILLER::65BRUCES"}, {"column": "MortgageMostRecentLenderName", "value": "PICKARD TR (RT)", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01460:MILLER::65BRUCES"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01460:BEATON::38WHITEP"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FED'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01460:BEATON::38WHITEP"}, {"column": "MortgageMostRecentLenderName", "value": "PACIFIC UNION FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01460:LAPIN::212HARWO"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01468:CARIGNAN::714SOUTH"}, {"column": "MortgageMostRecentLenderName", "value": "LOANDEPOT.COM LLC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01475:BRUNEL::20SUNSET"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01507:MICHAELSON::372STAFF"}, {"column": "CreditRating", "value": "H", "pattern": "/^[A-F]$/", "STHHLD": "01515:APGAR::103BLUEB"}, {"column": "CreditRating", "value": "H", "pattern": "/^[A-F]$/", "STHHLD": "01515:APGAR::103BLUEB"}, {"column": "MortgageMostRecentLenderName", "value": "INLAND BK&TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01520:OTOOLE::FARRAGUT"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01527:CADORETTE::19ELMWOO"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01527:CADORETTE::19ELMWOO"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01527:SORA::16JACKIE"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01527:SORA::16JACKIE"}, {"column": "HomePurchasePrice", "value": "3", "pattern": "/^\\d{2,4}$/", "STHHLD": "01532:CARROLL::9WILSONR"}, {"column": "HomePurchasePrice", "value": "3", "pattern": "/^\\d{2,4}$/", "STHHLD": "01532:CARROLL::9WILSONR"}, {"column": "MortgageMostRecentLenderName", "value": "TOWNE & CNTRY MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01535:PERRY::18MOUNTG"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01535:PERRY::18MOUNTG"}, {"column": "MortgageMostRecentLenderName", "value": "TOWNE & CNTRY MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01535:PERRY::18MOUNTG"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01535:PERRY::18MOUNTG"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01536:MAGNANT::19BRIGHA"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01543:YARWOOD::BRINTNAL"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE MASTER INC/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01545:PRANATHARTHIHAR::1FARMSHY"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01569:MONTANI::234HAZEL"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MILFORD FED S&L ASSOC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01569:DAVIS::183CHOCO"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01569:MONTANI::234HAZEL"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01570:ELDREDGE::408HIGHS"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01570:ELDREDGE::408HIGHS"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01570:ELDREDGE::408HIGHS"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01570:ELDREDGE::408HIGHS"}, {"column": "MortgageMostRecentLenderName", "value": "INLAND BK&TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01571:BOURASSA::3RD6AVE"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:SHUFFLEBARGER::STONEBRI"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:SHUFFLEBARGER::STONEBRI"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:SHUFFLEBARGER::STONEBRI"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:SHUFFLEBARGER::STONEBRI"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:SHUFFLEBARGER::STONEBRI"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:WATSON::SHEPHERD"}, {"column": "MortgageMostRecentLenderName", "value": "LOANDEPOT.COM LLC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:BUDADA::44SIMEON"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/RI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01581:BOTROUS::5LYDIASP"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MILFORD FED S&L ASSOC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01588:CULLEN::HERITAGE"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MILFORD FED S&L ASSOC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01588:CULLEN::HERITAGE"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "MILFORD FED S&L ASSOC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01588:CULLEN::HERITAGE"}, {"column": "MortgageMostRecentLenderName", "value": "MILFORD FED S&L ASSOC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01588:SETTE::29SWIFTR"}, {"column": "MortgageMostRecentLenderName", "value": "MILFORD FED S&L ASSOC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01588:SETTE::29SWIFTR"}, {"column": "MortgageMostRecentLenderName", "value": "MILFORD FED S&L ASSOC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01588:SETTE::29SWIFTR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01602:KRISTAN::100WATER"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01602:KRISTAN::100WATER"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01602:KRISTAN::100WATER"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01602:KRISTAN::100WATER"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01602:KRISTAN::100WATER"}, {"column": "MortgagePurchaseLoanTypeCode", "value": "V", "pattern": "/^[A-P]$/", "STHHLD": "01602:RODRIGUEZ::19LENOXS"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "MortgageMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01603:KURETA::26NUTMEG"}, {"column": "Mortgage2ndPurchaseAmount", "value": "3675", "pattern": "/^\\d{5,6}$/", "STHHLD": "01603:POTVIN::57TIMROD"}, {"column": "Mortgage2ndPurchaseAmount", "value": "3675", "pattern": "/^\\d{5,6}$/", "STHHLD": "01603:POTVIN::57TIMROD"}, {"column": "HomeSalesTransactionCode", "value": "C", "pattern": "/^[R,N]$/", "STHHLD": "01603:MAKA::109LAKEW"}, {"column": "MortgageMostRecentLenderName", "value": "DRAPER & KRAMER MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01605:CHARLESMORENO::DARLING5"}, {"column": "MortgageMostRecentLenderName", "value": "DRAPER & KRAMER MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01605:CHARLESMORENO::DARLING5"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01607:MANDELLA::312GREEN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01609:BROWN::45DRURYL"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01609:BROWN::45DRURYL"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "UMASS/FIVE COLLEGE FCU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01609:BROWN::45DRURYL"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01610:RIOS::CHELSEA3"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01701:TATSUNO::PLEASANT"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01702:BRISKEY::103BTERR"}, {"column": "MortgageMostRecentLenderName", "value": "HOME SVGS/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01702:GAGE::127BISHO"}, {"column": "MortgageMostRecentLenderName", "value": "ARBOR NAT'L MTG INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01742:FELSHIN::SUNNYSID"}, {"column": "MortgageMostRecentLenderName", "value": "ARBOR NAT'L MTG INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01742:FELSHIN::SUNNYSID"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01742:IGOE::20PARKER"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01746:LIBERTY::97NORFOL"}, {"column": "MortgageMostRecentLenderName", "value": "MB FIN'L BK NA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01748:ANGELO::9EMMADR"}, {"column": "MortgageMostRecentLenderName", "value": "MB FIN'L BK NA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01748:ANGELO::9EMMADR"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01749:FRENCH::86BRIGHA"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01749:FRENCH::86BRIGHA"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01749:FRENCH::86BRIGHA"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01749:FRENCH::86BRIGHA"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01749:FRENCH::86BRIGHA"}, {"column": "Religion", "value": "K", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01752:THORATI::13INDIAN"}, {"column": "Religion", "value": "K", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01752:THORATI::13INDIAN"}, {"column": "Religion", "value": "K", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01752:THORATI::13INDIAN"}, {"column": "MortgageMostRecentLenderName", "value": "MARGARETTEN & CO INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:CLEMENT::66PRESTO"}, {"column": "MortgageMostRecentLenderName", "value": "MARGARETTEN & CO INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:CLEMENT::66PRESTO"}, {"column": "MortgageMostRecentLenderName", "value": "MARGARETTEN & CO INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:CLEMENT::66PRESTO"}, {"column": "MortgageMostRecentLenderName", "value": "MARGARETTEN & CO INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:CLEMENT::66PRESTO"}, {"column": "MortgageMostRecentLenderName", "value": "MARGARETTEN & CO INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:CLEMENT::66PRESTO"}, {"column": "MortgageMostRecentLenderName", "value": "MARGARETTEN & CO INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:CLEMENT::66PRESTO"}, {"column": "MortgageMostRecentLenderName", "value": "MARGARETTEN & CO INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:CLEMENT::66PRESTO"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:DEMPSEY::136SUDBU"}, {"column": "MortgageMostRecentLenderName", "value": "CHEMICAL RESID'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:DEMPSEY::136SUDBU"}, {"column": "HomeSalesTransactionCode", "value": "S", "pattern": "/^[R,N]$/", "STHHLD": "01752:MATEO::MECHANIC"}, {"column": "MortgageMostRecentLenderName", "value": "55 MECHANIC TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:MATEO::MECHANIC"}, {"column": "HomeSalesTransactionCode", "value": "S", "pattern": "/^[R,N]$/", "STHHLD": "01752:MATEO::MECHANIC"}, {"column": "MortgageMostRecentLenderName", "value": "55 MECHANIC TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:MATEO::MECHANIC"}, {"column": "MortgageMostRecentLenderName", "value": "DRAPER & KRAMER MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01752:AGRAWAL::20430BRO"}, {"column": "MortgageMostRecentLenderName", "value": "PRUDENTIAL INS/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01757:VALENTE::43JANOCK"}, {"column": "MortgageMostRecentLenderName", "value": "PRUDENTIAL INS/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01757:VALENTE::43JANOCK"}, {"column": "MortgageMostRecentLenderName", "value": "PRUDENTIAL INS/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01757:VALENTE::43JANOCK"}, {"column": "MortgageMostRecentLenderName", "value": "PRUDENTIAL INS/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01757:VALENTE::43JANOCK"}, {"column": "MortgageMostRecentLenderName", "value": "PRUDENTIAL INS/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01757:VALENTE::43JANOCK"}, {"column": "MortgageMostRecentLenderName", "value": "PRUDENTIAL INS/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01757:VALENTE::43JANOCK"}, {"column": "MortgageMostRecentLenderName", "value": "US TRUST CO/MIDDLESEX", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "MortgageMostRecentLenderName", "value": "US TRUST CO/MIDDLESEX", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "HomeFuel", "value": "E", "pattern": "/^[G]$/", "STHHLD": "01760:BLACK::40RATHBU"}, {"column": "HomeFuel", "value": "E", "pattern": "/^[G]$/", "STHHLD": "01760:BLACK::40RATHBU"}, {"column": "HomeFuel", "value": "E", "pattern": "/^[G]$/", "STHHLD": "01760:BLACK::40RATHBU"}, {"column": "HomeFuel", "value": "E", "pattern": "/^[G]$/", "STHHLD": "01760:BLACK::40RATHBU"}, {"column": "MortgageMostRecentLenderName", "value": "US TRUST CO/MIDDLESEX", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "MortgageMostRecentLenderName", "value": "US TRUST CO/MIDDLESEX", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "MortgageMostRecentLenderName", "value": "US TRUST CO/MIDDLESEX", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "H&R BLOCK MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01760:TOBIN::COACHMAN"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01770:LEVY::140MAINS"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01770:LEVY::140MAINS"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01770:LEVY::140MAINS"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01770:LEVY::140MAINS"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01770:LEVY::140MAINS"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01770:LEVY::140MAINS"}, {"column": "MortgageMostRecentLenderName", "value": "PNC MTG CORP/AMERICA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01770:LEVY::140MAINS"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01778:PRATT::11MEADOW"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01778:PRATT::11MEADOW"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01778:PRATT::11MEADOW"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01778:PRATT::11MEADOW"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L SVC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01778:PRATT::11MEADOW"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:CARR::39WARREN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:CARR::39WARREN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:CARR::39WARREN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:CARR::39WARREN"}, {"column": "MortgageMostRecentLenderName", "value": "NORTHERN BK&TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:PARADISO::CORTLAND"}, {"column": "MortgageMostRecentLenderName", "value": "FREEMONT INVS & LN", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:OLIVEIRA::89SALEMS"}, {"column": "MortgageMostRecentLenderName", "value": "FREEMONT INVS & LN", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:OLIVEIRA::89SALEMS"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01801:SHIU::CALLAHAN"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01801:SHIU::CALLAHAN"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:CARR::39WARREN"}, {"column": "MortgageMostRecentLenderName", "value": "FREEMONT INVS & LN", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:OLIVEIRA::89SALEMS"}, {"column": "MortgageMostRecentLenderName", "value": "FREEMONT INVS & LN", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:OLIVEIRA::89SALEMS"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01801:SHIU::CALLAHAN"}, {"column": "Religion", "value": "S", "pattern": "/^[B,C,X,P,O,B,J,H,G,I]$/", "STHHLD": "01801:SHIU::CALLAHAN"}, {"column": "MortgageMostRecentLenderName", "value": "NORTHERN BK&TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:PARADISO::CORTLAND"}, {"column": "MortgageMostRecentLenderName", "value": "NORTHERN BK&TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:PARADISO::CORTLAND"}, {"column": "MortgageMostRecentLenderName", "value": "NORTHERN BK&TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01801:PARADISO::CORTLAND"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01801:HUYNH::MARILYN3"}, {"column": "MortgageMostRecentLenderName", "value": "TAYLOR BEAN & WHITTAKER", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01803:MITZAJR::102BEDFO"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01803:PIAZZA::48FRANCI"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "P", "pattern": "/^[A-C]$/", "STHHLD": "01803:PIAZZA::48FRANCI"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "ENTERPRISE BK & TR CO", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "ENTERPRISE BK & TR CO", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "ENTERPRISE BK & TR CO", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "ENTERPRISE BK & TR CO", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "Mortgage2ndMostRecentAmount", "value": "1000000", "pattern": "/^\\d{4,6}$/", "STHHLD": "01810:BERNARDI::120ABBOT"}, {"column": "MortgageMostRecentLenderName", "value": "BOSTON SAFE DEPOSIT & TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:BERNARDI::120ABBOT"}, {"column": "Mortgage2ndMostRecentAmount", "value": "1000000", "pattern": "/^\\d{4,6}$/", "STHHLD": "01810:BERNARDI::120ABBOT"}, {"column": "MortgageMostRecentLenderName", "value": "BOSTON SAFE DEPOSIT & TR", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:BERNARDI::120ABBOT"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "ENTERPRISE BK & TR CO", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "ENTERPRISE BK & TR CO", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01810:ZALANSKAS::34BIRCHR"}, {"column": "MortgageMostRecentLenderName", "value": "NORTHERN BK&TR CO", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01821:LURIE::GOVERNOR"}, {"column": "CreditRating", "value": "H", "pattern": "/^[A-F]$/", "STHHLD": "01824:GUNNIS::346NORTH"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST MAGNUS FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:BELL::136NASHU"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST MAGNUS FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:BELL::136NASHU"}, {"column": "MortgageMostRecentLenderName", "value": "FIRST MAGNUS FIN'L CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:BELL::136NASHU"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01826:METROS::320MARSH"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:ERNEST::15WAGONW"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:ERNEST::15WAGONW"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:ERNEST::15WAGONW"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01826:METROS::320MARSH"}, {"column": "MortgageMostRecentLenderName", "value": "2004 25 LLC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:ABOU::FREEDOM7"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:WILLIAMSON::66BURNAB"}, {"column": "MortgageMostRecentLenderName", "value": "LEADERONE FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:LECLERC::701MARSH"}, {"column": "MortgageMostRecentLenderName", "value": "LEADERONE FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01826:LECLERC::701MARSH"}, {"column": "MortgageMostRecentLenderName", "value": "JEANNE D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01827:THOMANN::24HIGHST"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01830:LONG::HOMESTEA"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "HUD-HOUSING/URBAN DEV", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01830:LONG::HOMESTEA"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01830:LONG::HOMESTEA"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "HUD-HOUSING/URBAN DEV", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01830:LONG::HOMESTEA"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01830:KELLY::16ELMST"}, {"column": "MortgageMostRecentLenderName", "value": "ARBOR NAT'L MTG INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01832:PAROLISIJR::36ARTHUR"}, {"column": "MortgageMostRecentLenderName", "value": "ARBOR NAT'L MTG INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01832:PAROLISIJR::36ARTHUR"}, {"column": "MortgageMostRecentLenderName", "value": "CREDIT UNION-CNTY/MUNI MI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01833:SHAW::25MOLLOY"}, {"column": "MortgageMostRecentLenderName", "value": "CREDIT UNION-CNTY/MUNI MI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01833:SHAW::25MOLLOY"}, {"column": "MortgageMostRecentLenderName", "value": "CREDIT UNION-CNTY/MUNI MI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01833:SHAW::25MOLLOY"}, {"column": "MortgageMostRecentLenderName", "value": "CREDIT UNION-CNTY/MUNI MI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01833:SHAW::25MOLLOY"}, {"column": "MortgageMostRecentLenderName", "value": "CREDIT UNION-CNTY/MUNI MI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01833:SHAW::25MOLLOY"}, {"column": "MortgageMostRecentLenderName", "value": "CREDIT UNION-CNTY/MUNI MI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01833:SHAW::25MOLLOY"}, {"column": "MortgageMostRecentLenderName", "value": "INSTITUTION FOR SVGS/NEWB", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01835:POPOLOSKI::244BOXFO"}, {"column": "MortgageMostRecentLenderName", "value": "INSTITUTION FOR SVGS/NEWB", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01835:POPOLOSKI::244BOXFO"}, {"column": "MortgageMostRecentLenderName", "value": "INSTITUTION FOR SVGS/NEWB", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01835:POPOLOSKI::244BOXFO"}, {"column": "MortgageMostRecentLenderName", "value": "INSTITUTION FOR SVGS/NEWB", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01835:POPOLOSKI::244BOXFO"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01843:MORAN::2942SALE"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01845:HARRIGAN::CAMPBELL"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01845:HARRIGAN::CAMPBELL"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01845:HARRIGAN::CAMPBELL"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01845:HARRIGAN::CAMPBELL"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01845:HARRIGAN::CAMPBELL"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01845:HARRIGAN::CAMPBELL"}, {"column": "CreditRating", "value": "G", "pattern": "/^[A-F]$/", "STHHLD": "01862:SCHWANDER::26SHELDO"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE MASTER INC/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01864:ABCUNAS::ANTHONY7"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE MASTER INC/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01864:ABCUNAS::ANTHONY7"}, {"column": "MortgageMostRecentLenderName", "value": "DRAPER & KRAMER MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01864:GREEN::GREENBRI"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "DRAPER & KRAMER MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01864:GREEN::GREENBRI"}, {"column": "MortgageMostRecentLenderName", "value": "RADIUS FIN'L GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01864:AMIT::DEERFIEL"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01864:KIMBALL::FIELDSTO"}, {"column": "MortgageMostRecentLenderName", "value": "AMERICAN RESID'L MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:BREEN::109OAKST"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/RI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:GESMUNDO::SUNNYSID"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/RI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:GESMUNDO::SUNNYSID"}, {"column": "MortgageMostRecentLenderName", "value": "AMERICAN RESID'L MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:BREEN::109OAKST"}, {"column": "MortgageMostRecentLenderName", "value": "AMERICAN RESID'L MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:BREEN::109OAKST"}, {"column": "MortgageMostRecentLenderName", "value": "AMERICAN RESID'L MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:BREEN::109OAKST"}, {"column": "MortgageMostRecentLenderName", "value": "AMERICAN RESID'L MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:BREEN::109OAKST"}, {"column": "MortgageMostRecentLenderName", "value": "AMERICAN RESID'L MTG", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:BREEN::109OAKST"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/RI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:GESMUNDO::SUNNYSID"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "CITIZENS BK/RI", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01867:GESMUNDO::SUNNYSID"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01876:HARRING::109APACH"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01876:HARRING::109APACH"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01876:SMITH::231MARSH"}, {"column": "MortgageMostRecentLenderName", "value": "NEW FED'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01876:SMITH::231MARSH"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01876:SMITH::231MARSH"}, {"column": "MortgageMostRecentLenderName", "value": "NEW FED'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01876:SMITH::231MARSH"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01876:SMITH::231MARSH"}, {"column": "MortgageMostRecentLenderName", "value": "NEW FED'L MTG CORP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01876:SMITH::231MARSH"}, {"column": "MortgageMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01876:HARRING::109APACH"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "GMAC MTG CORP/PA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01876:HARRING::109APACH"}, {"column": "MortgageMostRecentLenderName", "value": "JEAN D'ARC CU", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01886:PALISOUL::SANDSTON"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01887:CARTA::107GLENR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01887:CARTA::107GLENR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01887:CARTA::107GLENR"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "FLEET NAT'L BK", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01887:CARTA::107GLENR"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE MASTER INC/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01890:KUO::23HILLST"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE MASTER INC/MA", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01890:KUO::23HILLST"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "AURORA FIN'L GRP INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01902:SCIANATICO::2PEIRCER"}, {"column": "Mortgage2ndMostRecentLenderName", "value": "AURORA FIN'L GRP INC", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01902:SCIANATICO::2PEIRCER"}, {"column": "Mortgage2ndMostRecentLoanTypeCode", "value": "F", "pattern": "/^[A-C]$/", "STHHLD": "01902:GRIFFIN::487EASTE"}, {"column": "MortgageMostRecentLenderName", "value": "PARAMOUNT RESID'L MTG GRP", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01902:SULMY::ADDISON1"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "HOMECOMING FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01905:MCKANAS::110PURDO"}, {"column": "MortgageMostRecentLenderName", "value": "LOANDEPOT.COM", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01913:HAWKINS::10HOYTAV"}, {"column": "MortgageMostRecentLenderName", "value": "LOANDEPOT.COM", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01913:HAWKINS::10HOYTAV"}, {"column": "MortgageMostRecentLenderName", "value": "MORTGAGE FIN'L", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01913:CHAPMAN::106KIMBA"}, {"column": "MortgageMostRecentLenderName", "value": "LOANDEPOT.COM", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01913:HAWKINS::10HOYTAV"}, {"column": "MortgageMostRecentLenderName", "value": "COMMUNITY CU/LYNN", "pattern": "/^[A-Z\\s-]+$/", "STHHLD": "01913:LAFRAZIA::3DEVINGL"}]